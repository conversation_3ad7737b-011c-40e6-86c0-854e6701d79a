// webauthn.ts
import {
    startRegistration,
    startAuthentication,
    browserSupportsWebAuthn,
} from '@simplewebauthn/browser';

import { useAuthManagement } from 'stores/auth-management';
import {useLogins} from 'stores/logins';
import {SessionStorage} from 'symbol-auth-client';
import {useAuth} from 'stores/auth';

type FeathersApp = {
    service: (path: string) => {
        create: (data: any) => Promise<any>
    }
};

export function isWebAuthnSupported() {
    return browserSupportsWebAuthn();
}

/**
 * Register a new passkey for a user.
 * Calls your `webauthn` service over the socket:
 *  - create({ action: 'registrationOptions', ... })
 *  - startRegistration(options)
 *  - create({ action: 'registrationVerify', ... })
 */
export async function registerPasskey(
    app: FeathersApp,
    opts: { loginId: string; usernameField?: string; displayName?: string }
): Promise<{ success: boolean; message: string }> {
    if (!browserSupportsWebAuthn()) {
        return { success: false, message: 'Your browser does not support WebAuthn' };
    }

    try {
        const args = [{ loginAttempts: { $push: { $each: [new Date()], $position: 0, $slice: 20 }}}, { query: { loginOptions: { usernameField:opts.usernameField, rpID: SessionStorage.getItem('fqdn') } }}]
        let method = 'create';
        if(opts.loginId){
            method = 'patch';
            args.unshift(opts.loginId as any);
        }
        // 1) Get options from server
        const options = await useLogins()[method](...args)

        // 2) Browser ceremony
        const credential = await startRegistration(options);

        // 3) Verify & persist authenticator on server
        const verification = await useAuth().authenticate({
            strategy: 'webauthn',
            loginId: opts.loginId,
            credential,
        });

        if (verification?.verified) {
            return { success: true, message: 'Passkey registered' };
        }
        return { success: false, message: `Verification failed: ${JSON.stringify(verification)}` };
    } catch (err: any) {
        // Common: NotAllowedError if user cancels, InvalidStateError if already registered, etc.
        return { success: false, message: err?.message ?? 'Registration error' };
    }
}

/**
 * Authenticate with a passkey.
 *  - create({ action: 'authenticationOptions', ... })
 *  - startAuthentication(options)
 *  - authentication.create({ strategy: 'webauthn', credential })
 *
 * If you omit loginId, this will attempt **usernameless** sign-in.
 */
export async function loginWithPasskey(
    app: FeathersApp,
    opts: { loginId?: string } = {}
): Promise<{ success: boolean; message: string; authResult?: any }> {
    if (!browserSupportsWebAuthn()) {
        return { success: false, message: 'Your browser does not support WebAuthn' };
    }

    try {
        // 1) Get options (loginId optional for usernameless)
        const options = await app.service('webauthn').create({
            action: 'authenticationOptions',
            loginId: opts.loginId ?? null,
        });

        // 2) Browser ceremony
        const credential = await startAuthentication(options);

        // 3) Ask Feathers to issue JWT via your `webauthn` strategy
        const authResult = await app.service('authentication').create({
            strategy: 'webauthn',
            credential,
        });

        return { success: true, message: 'Signed in', authResult };
    } catch (err: any) {
        return { success: false, message: err?.message ?? 'Authentication error' };
    }
}
