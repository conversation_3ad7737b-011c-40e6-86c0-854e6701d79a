<template>
  <div>

  </div>
</template>

<script setup>
  import {computed, watch} from 'vue';
  import {isWebAuthnSupported} from 'components/auth/webauthn/webauthn';

  const emit = defineEmits(['update:on']);
  const props = defineProps({
    on: Boolean
  })

  const isWebAuthn = computed(() => isWebAuthnSupported());
  watch(isWebAuthn, (nv) => {
    emit('update:on', !!nv);
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
